using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using UnityEngine.UI;
using System;
public class BikeCamera : MonoBehaviour
{



    public Transform target;
    public Transform BikerMan;

    public float smooth = 0.3f;
    public float distance = 5.0f;
    public float height = 1.0f;
    public float Angle = 20;

    [Header("Auto Target Assignment")]
    public bool autoTargetNearestBike = true;
    public BikeUIController bikeUIController;

    [Header("Auto BikerMan Assignment")]
    public bool autoAssignBikerMan = true;
    public bool searchInBikeHierarchy = true;
    public string bikerManTag = "BikerMan";
    public float bikerManDetectionDistance = 10f;

    public List<Transform> cameraSwitchView;
    public BikeUIClass BikeUI;

    public LayerMask lineOfSightMask = 0;

   



    private float yVelocity = 0.0f;
    private float xVelocity = 0.0f;
    [HideInInspector]
    public int Switch;

    private int gearst = 0;
    private float thisAngle = -150;
    private float restTime = 0.0f;


    private Rigidbody myRigidbody;



    private BikeControl bikeScript;



    [System.Serializable]
    public class BikeUIClass
    {

        public Image tachometerNeedle;
        public Image barShiftGUI;

        public Text speedText;
        public Text GearText;

    }


    


    ////////////////////////////////////////////// TouchMode (Control) ////////////////////////////////////////////////////////////////////


    private int PLValue = 0;


    public void PoliceLightSwitch()
    {

        if (!target.gameObject.GetComponent<PoliceLights>()) return;

        PLValue++;

        if (PLValue > 1) PLValue = 0;

        if (PLValue == 1)
            target.gameObject.GetComponent<PoliceLights>().activeLight = true;

        if (PLValue == 0)
            target.gameObject.GetComponent<PoliceLights>().activeLight = false;


    }


    public void CameraSwitch()
    {
        Switch++;
        if (Switch > cameraSwitchView.Count) { Switch = 0; }
    }


    public void BikeAccelForward(float amount)
    {
       bikeScript.accelFwd = amount;
    }

    public void BikeAccelBack(float amount)
    {
        bikeScript.accelBack = amount;
    }

    public void BikeSteer(float amount)
    {
        bikeScript.steerAmount = amount;
    }

    public void BikeHandBrake(bool HBrakeing)
    {
        bikeScript.brake = HBrakeing;
    }

    public void BikeShift(bool Shifting)
    {
        bikeScript.shift = Shifting;
    }



    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


    

    public void RestBike()
    {

        if (restTime == 0)
        {
            myRigidbody.AddForce(Vector3.up * 500000);
            myRigidbody.MoveRotation(Quaternion.Euler(0, transform.eulerAngles.y, 0));
            restTime = 2.0f;
        }

    }




    public void ShowBikeUI()
    {



        gearst = bikeScript.currentGear;
        BikeUI.speedText.text = ((int)bikeScript.speed).ToString();




        if (bikeScript.bikeSetting.automaticGear)
        {

            if (gearst > 0 && bikeScript.speed > 1)
            {
                BikeUI.GearText.color = Color.green;
                BikeUI.GearText.text = gearst.ToString();
            }
            else if (bikeScript.speed > 1)
            {
                BikeUI.GearText.color = Color.red;
                BikeUI.GearText.text = "R";
            }
            else
            {
                BikeUI.GearText.color = Color.white;
                BikeUI.GearText.text = "N";
            }

        }
        else
        {

            if (bikeScript.NeutralGear)
            {
                BikeUI.GearText.color = Color.white;
                BikeUI.GearText.text = "N";
            }
            else
            {
                if (bikeScript.currentGear != 0)
                {
                    BikeUI.GearText.color = Color.green;
                    BikeUI.GearText.text = gearst.ToString();
                }
                else
                {

                    BikeUI.GearText.color = Color.red;
                    BikeUI.GearText.text = "R";
                }
            }

        }





        thisAngle = (bikeScript.motorRPM / 20) - 175;
        thisAngle = Mathf.Clamp(thisAngle, -180, 90);

        BikeUI.tachometerNeedle.rectTransform.rotation = Quaternion.Euler(0, 0, -thisAngle);
        BikeUI.barShiftGUI.rectTransform.localScale = new Vector3(bikeScript.powerShift / 100.0f, 1, 1);

    }



    void Start()
    {
        // Auto-find BikeUIController if not assigned
        if (autoTargetNearestBike && bikeUIController == null)
        {
            bikeUIController = FindObjectOfType<BikeUIController>();
        }

        if (target != null)
        {
            bikeScript = (BikeControl)target.GetComponent<BikeControl>();
            myRigidbody = target.GetComponent<Rigidbody>();
            cameraSwitchView = bikeScript.bikeSetting.cameraSwitchView;
            BikerMan = bikeScript.bikeSetting.bikerMan;
        }
    }




    void Update()
    {
        // Auto-target nearest bike if enabled
        if (autoTargetNearestBike && bikeUIController != null)
        {
            BikeControl currentBike = bikeUIController.GetCurrentBike();
            if (currentBike != null && currentBike.transform != target)
            {
                SetTarget(currentBike.transform);
            }
        }

        // Auto-assign nearest BikerMan if enabled
        if (autoAssignBikerMan && target != null)
        {
            Transform nearestBikerMan = FindNearestBikerMan();
            if (nearestBikerMan != null && nearestBikerMan != BikerMan)
            {
                BikerMan = nearestBikerMan;
                Debug.Log($"Auto-assigned nearest BikerMan: {nearestBikerMan.name}");
            }
        }

        if (!target) return;

        bikeScript = (BikeControl)target.GetComponent<BikeControl>();
        myRigidbody = target.GetComponent<Rigidbody>();


        if (Input.GetKeyDown(KeyCode.G))
        {
            RestBike();
        }


        if (Input.GetKeyDown(KeyCode.R))
        {
            Application.LoadLevel(Application.loadedLevel);
        }


        if (Input.GetKeyDown(KeyCode.E))
        {
            PoliceLightSwitch();
        }


        if (restTime!=0.0f)
        restTime=Mathf.MoveTowards(restTime ,0.0f,Time.deltaTime);




        ShowBikeUI();

        GetComponent<Camera>().fieldOfView = Mathf.Clamp(bikeScript.speed / 10.0f + 60.0f, 60, 90.0f);



        if (Input.GetKeyDown(KeyCode.C))
        {
            Switch++;
            if (Switch > cameraSwitchView.Count) { Switch = 0; }
        }


        if (!bikeScript.crash)
        {
            if (Switch == 0)
            {
                // Damp angle from current y-angle towards target y-angle

                float xAngle = Mathf.SmoothDampAngle(transform.eulerAngles.x,
               target.eulerAngles.x + Angle, ref xVelocity, smooth);

                float yAngle = Mathf.SmoothDampAngle(transform.eulerAngles.y,
                target.eulerAngles.y, ref yVelocity, smooth);

                // Look at the target
                transform.eulerAngles = new Vector3(Angle, yAngle, 0.0f);

                var direction = transform.rotation * -Vector3.forward;
                var targetDistance = AdjustLineOfSight(target.position + new Vector3(0, height, 0), direction);


                transform.position = target.position + new Vector3(0, height, 0) + direction * targetDistance;


            }
            else
            {

                transform.position = cameraSwitchView[Switch - 1].position;
                transform.rotation = Quaternion.Lerp(transform.rotation, cameraSwitchView[Switch - 1].rotation, Time.deltaTime * 5.0f);

            }
        }
        else
        {
            Vector3 look = BikerMan.position - transform.position;
            transform.rotation = Quaternion.LookRotation(look);
        }

    }



    float AdjustLineOfSight(Vector3 target, Vector3 direction)
    {


        RaycastHit hit;

        if (Physics.Raycast(target, direction, out hit, distance, lineOfSightMask.value))
            return hit.distance;
        else
            return distance;

    }

    // Method to set new target bike
    public void SetTarget(Transform newTarget)
    {
        if (newTarget == null) return;

        target = newTarget;

        // Update bike script and rigidbody references
        bikeScript = target.GetComponent<BikeControl>();
        myRigidbody = target.GetComponent<Rigidbody>();

        if (bikeScript != null)
        {
            cameraSwitchView = bikeScript.bikeSetting.cameraSwitchView;

            // Auto-assign BikerMan if enabled, otherwise use bike's default BikerMan
            if (autoAssignBikerMan)
            {
                Transform nearestBikerMan = FindNearestBikerMan();
                BikerMan = nearestBikerMan != null ? nearestBikerMan : bikeScript.bikeSetting.bikerMan;
            }
            else
            {
                BikerMan = bikeScript.bikeSetting.bikerMan;
            }

            Switch = 0; // Reset camera switch to default view
        }

        Debug.Log($"Camera target set to: {newTarget.name}");
    }

    // Method to find nearest BikerMan with tag (including nested children)
    private Transform FindNearestBikerMan()
    {
        if (target == null) return null;

        Transform nearestBikerMan = null;
        float closestDistance = bikerManDetectionDistance;

        // First, check if there's a BikerMan in the current bike's hierarchy (any level deep)
        if (searchInBikeHierarchy)
        {
            Transform bikerManInHierarchy = FindBikerManInHierarchy(target);
            if (bikerManInHierarchy != null)
            {
                // If found in hierarchy, use it directly (distance = 0 since it's part of the bike)
                Debug.Log($"Found BikerMan in bike hierarchy: {bikerManInHierarchy.name}");
                return bikerManInHierarchy;
            }
        }

        // If not found in hierarchy, search by tag in the scene
        GameObject[] bikerMans = GameObject.FindGameObjectsWithTag(bikerManTag);

        foreach (GameObject bikerMan in bikerMans)
        {
            if (bikerMan != null && target != null)
            {
                float distance = Vector3.Distance(target.position, bikerMan.transform.position);
                if (distance <= closestDistance)
                {
                    closestDistance = distance;
                    nearestBikerMan = bikerMan.transform;
                }
            }
        }

        return nearestBikerMan;
    }

    // Method to recursively search for BikerMan in bike's hierarchy (any level deep)
    private Transform FindBikerManInHierarchy(Transform parent)
    {
        if (parent == null) return null;

        // Check if current object has the BikerMan tag
        if (parent.CompareTag(bikerManTag))
        {
            return parent;
        }

        // Check if current object's name contains "BikerMan" (case insensitive)
        if (parent.name.ToLower().Contains("bikerman") || parent.name.ToLower().Contains("biker"))
        {
            return parent;
        }

        // Recursively search all children (any level deep)
        for (int i = 0; i < parent.childCount; i++)
        {
            Transform foundBikerMan = FindBikerManInHierarchy(parent.GetChild(i));
            if (foundBikerMan != null)
            {
                return foundBikerMan;
            }
        }

        return null;
    }

    // Method to manually set BikerMan
    public void SetBikerMan(Transform newBikerMan)
    {
        BikerMan = newBikerMan;
        Debug.Log($"BikerMan manually set to: {(newBikerMan != null ? newBikerMan.name : "null")}");
    }

    // Method to manually enable/disable auto targeting
    public void EnableAutoTargeting(bool enable)
    {
        autoTargetNearestBike = enable;
        Debug.Log($"Auto targeting {(enable ? "enabled" : "disabled")}");
    }

    // Method to enable/disable auto BikerMan assignment
    public void EnableAutoBikerManAssignment(bool enable)
    {
        autoAssignBikerMan = enable;
        Debug.Log($"Auto BikerMan assignment {(enable ? "enabled" : "disabled")}");
    }


}

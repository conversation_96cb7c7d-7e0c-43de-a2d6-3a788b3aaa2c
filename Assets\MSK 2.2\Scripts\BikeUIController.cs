using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

public class BikeUIController : MonoBehaviour
{
    [Header("Touch UI Buttons")]
    public Button accelButton;
    public Button brakeButton;
    public Button handBrakeButton;
    public Button rightButton;
    public Button leftButton;
    public Button nitroButton;

    [Header("Current Active Bike")]
    public BikeControl currentBike;

    [Header("Auto Bike Assignment")]
    public bool autoAssignBike = true;
    public string motorbikeTag = "Motorbike";
    public float detectionDistance = 4f;
    public Transform Player;

    [Header("Auto Find Buttons")]
    public bool autoFindButtons = true;
    public string accelButtonName = "AccelButton";
    public string brakeButtonName = "BrakeButton";
    public string handBrakeButtonName = "HandBrakeButton";
    public string rightButtonName = "RightButton";
    public string leftButtonName = "LeftButton";
    public string nitroButtonName = "NitroButton";

    private BikeControl previousBike;

    private void Start()
    {
        if (autoFindButtons)
        {
            FindUIButtons();
        }
        SetupButtonEvents();
    }

    private void Update()
    {
        if (autoAssignBike && Player != null)
        {
            AutoAssignNearestBike();
        }
    }

    private void FindUIButtons()
    {
        // Auto find buttons by name if not assigned
        if (accelButton == null)
            accelButton = GameObject.Find(accelButtonName)?.GetComponent<Button>();
        
        if (brakeButton == null)
            brakeButton = GameObject.Find(brakeButtonName)?.GetComponent<Button>();
        
        if (handBrakeButton == null)
            handBrakeButton = GameObject.Find(handBrakeButtonName)?.GetComponent<Button>();
        
        if (rightButton == null)
            rightButton = GameObject.Find(rightButtonName)?.GetComponent<Button>();
        
        if (leftButton == null)
            leftButton = GameObject.Find(leftButtonName)?.GetComponent<Button>();
        
        if (nitroButton == null)
            nitroButton = GameObject.Find(nitroButtonName)?.GetComponent<Button>();
    }

    public void SetActiveBike(BikeControl bike)
    {
        currentBike = bike;
    }

    private void AutoAssignNearestBike()
    {
        // Find the closest motorbike within detection distance
        GameObject closestMotorbike = FindClosestMotorbikeWithTag(motorbikeTag, detectionDistance);

        if (closestMotorbike != null)
        {
            BikeControl nearestBike = closestMotorbike.GetComponent<BikeControl>();

            // If we found a different bike than the current one, switch to it
            if (nearestBike != null && nearestBike != currentBike)
            {
                // Clear previous bike assignment
                if (previousBike != null && previousBike != nearestBike)
                {
                    previousBike = null;
                }

                // Assign new bike
                currentBike = nearestBike;
                previousBike = currentBike;

                Debug.Log($"Auto-assigned nearest bike: {nearestBike.name}");
            }
        }
        else
        {
            // No bike within range, clear assignment if we had one
            if (currentBike != null)
            {
                previousBike = currentBike;
                currentBike = null;
                Debug.Log("No bike within range, cleared assignment");
            }
        }
    }

    private GameObject FindClosestMotorbikeWithTag(string tag, float maxDistance)
    {
        GameObject[] motorbikes = GameObject.FindGameObjectsWithTag(tag);
        GameObject closestMotorbike = null;
        float closestDistance = maxDistance;

        foreach (GameObject motorbike in motorbikes)
        {
            if (motorbike != null && Player != null)
            {
                float distance = Vector3.Distance(Player.position, motorbike.transform.position);
                if (distance <= closestDistance)
                {
                    closestDistance = distance;
                    closestMotorbike = motorbike;
                }
            }
        }

        return closestMotorbike;
    }

    private void SetupButtonEvents()
    {
        // Setup Accel Button
        if (accelButton != null)
        {
            accelButton.onClick.RemoveAllListeners();
            var accelEventTrigger = accelButton.gameObject.GetComponent<EventTrigger>();
            if (accelEventTrigger == null)
                accelEventTrigger = accelButton.gameObject.AddComponent<EventTrigger>();

            accelEventTrigger.triggers.Clear();

            var pointerDown = new EventTrigger.Entry();
            pointerDown.eventID = EventTriggerType.PointerDown;
            pointerDown.callback.AddListener((data) => { AccelPressed(); });
            accelEventTrigger.triggers.Add(pointerDown);

            var pointerUp = new EventTrigger.Entry();
            pointerUp.eventID = EventTriggerType.PointerUp;
            pointerUp.callback.AddListener((data) => { AccelReleased(); });
            accelEventTrigger.triggers.Add(pointerUp);
        }

        // Setup Brake Button
        if (brakeButton != null)
        {
            brakeButton.onClick.RemoveAllListeners();
            var brakeEventTrigger = brakeButton.gameObject.GetComponent<EventTrigger>();
            if (brakeEventTrigger == null)
                brakeEventTrigger = brakeButton.gameObject.AddComponent<EventTrigger>();

            brakeEventTrigger.triggers.Clear();

            var pointerDown = new EventTrigger.Entry();
            pointerDown.eventID = EventTriggerType.PointerDown;
            pointerDown.callback.AddListener((data) => { BrakePressed(); });
            brakeEventTrigger.triggers.Add(pointerDown);

            var pointerUp = new EventTrigger.Entry();
            pointerUp.eventID = EventTriggerType.PointerUp;
            pointerUp.callback.AddListener((data) => { BrakeReleased(); });
            brakeEventTrigger.triggers.Add(pointerUp);
        }

        // Setup Hand Brake Button
        if (handBrakeButton != null)
        {
            handBrakeButton.onClick.RemoveAllListeners();
            var handBrakeEventTrigger = handBrakeButton.gameObject.GetComponent<EventTrigger>();
            if (handBrakeEventTrigger == null)
                handBrakeEventTrigger = handBrakeButton.gameObject.AddComponent<EventTrigger>();

            handBrakeEventTrigger.triggers.Clear();

            var pointerDown = new EventTrigger.Entry();
            pointerDown.eventID = EventTriggerType.PointerDown;
            pointerDown.callback.AddListener((data) => { HandBrakePressed(); });
            handBrakeEventTrigger.triggers.Add(pointerDown);

            var pointerUp = new EventTrigger.Entry();
            pointerUp.eventID = EventTriggerType.PointerUp;
            pointerUp.callback.AddListener((data) => { HandBrakeReleased(); });
            handBrakeEventTrigger.triggers.Add(pointerUp);
        }

        // Setup Right Button
        if (rightButton != null)
        {
            rightButton.onClick.RemoveAllListeners();
            var rightEventTrigger = rightButton.gameObject.GetComponent<EventTrigger>();
            if (rightEventTrigger == null)
                rightEventTrigger = rightButton.gameObject.AddComponent<EventTrigger>();

            rightEventTrigger.triggers.Clear();

            var pointerDown = new EventTrigger.Entry();
            pointerDown.eventID = EventTriggerType.PointerDown;
            pointerDown.callback.AddListener((data) => { RightPressed(); });
            rightEventTrigger.triggers.Add(pointerDown);

            var pointerUp = new EventTrigger.Entry();
            pointerUp.eventID = EventTriggerType.PointerUp;
            pointerUp.callback.AddListener((data) => { RightReleased(); });
            rightEventTrigger.triggers.Add(pointerUp);
        }

        // Setup Left Button
        if (leftButton != null)
        {
            leftButton.onClick.RemoveAllListeners();
            var leftEventTrigger = leftButton.gameObject.GetComponent<EventTrigger>();
            if (leftEventTrigger == null)
                leftEventTrigger = leftButton.gameObject.AddComponent<EventTrigger>();

            leftEventTrigger.triggers.Clear();

            var pointerDown = new EventTrigger.Entry();
            pointerDown.eventID = EventTriggerType.PointerDown;
            pointerDown.callback.AddListener((data) => { LeftPressed(); });
            leftEventTrigger.triggers.Add(pointerDown);

            var pointerUp = new EventTrigger.Entry();
            pointerUp.eventID = EventTriggerType.PointerUp;
            pointerUp.callback.AddListener((data) => { LeftReleased(); });
            leftEventTrigger.triggers.Add(pointerUp);
        }

        // Setup Nitro Button
        if (nitroButton != null)
        {
            nitroButton.onClick.RemoveAllListeners();
            var nitroEventTrigger = nitroButton.gameObject.GetComponent<EventTrigger>();
            if (nitroEventTrigger == null)
                nitroEventTrigger = nitroButton.gameObject.AddComponent<EventTrigger>();

            nitroEventTrigger.triggers.Clear();

            var pointerDown = new EventTrigger.Entry();
            pointerDown.eventID = EventTriggerType.PointerDown;
            pointerDown.callback.AddListener((data) => { NitroPressed(); });
            nitroEventTrigger.triggers.Add(pointerDown);

            var pointerUp = new EventTrigger.Entry();
            pointerUp.eventID = EventTriggerType.PointerUp;
            pointerUp.callback.AddListener((data) => { NitroReleased(); });
            nitroEventTrigger.triggers.Add(pointerUp);
        }
    }

    // Touch Control Methods
    public void AccelPressed()
    {
        if (currentBike != null)
            currentBike.AccelPressed();
    }

    public void AccelReleased()
    {
        if (currentBike != null)
            currentBike.AccelReleased();
    }

    public void BrakePressed()
    {
        if (currentBike != null)
            currentBike.BrakePressed();
    }

    public void BrakeReleased()
    {
        if (currentBike != null)
            currentBike.BrakeReleased();
    }

    public void HandBrakePressed()
    {
        if (currentBike != null)
            currentBike.HandBrakePressed();
    }

    public void HandBrakeReleased()
    {
        if (currentBike != null)
            currentBike.HandBrakeReleased();
    }

    public void RightPressed()
    {
        if (currentBike != null)
            currentBike.RightPressed();
    }

    public void RightReleased()
    {
        if (currentBike != null)
            currentBike.RightReleased();
    }

    public void LeftPressed()
    {
        if (currentBike != null)
            currentBike.LeftPressed();
    }

    public void LeftReleased()
    {
        if (currentBike != null)
            currentBike.LeftReleased();
    }

    public void NitroPressed()
    {
        if (currentBike != null)
            currentBike.NitroPressed();
    }

    public void NitroReleased()
    {
        if (currentBike != null)
            currentBike.NitroReleased();
    }

    // Method to reassign buttons at runtime (useful for prefab spawning)
    public void ReassignButtons()
    {
        FindUIButtons();
        SetupButtonEvents();
    }

    // Method to manually assign all buttons
    public void AssignButtons(Button accel, Button brake, Button handBrake, Button right, Button left, Button nitro)
    {
        accelButton = accel;
        brakeButton = brake;
        handBrakeButton = handBrake;
        rightButton = right;
        leftButton = left;
        nitroButton = nitro;
        
        SetupButtonEvents();
    }
}

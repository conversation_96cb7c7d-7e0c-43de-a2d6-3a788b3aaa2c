using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(BikeUIController))]
public class BikeUIControllerEditor : Editor
{
    private BikeUIController controller;
    private GUIStyle headerStyle;
    private GUIStyle brandingStyle;
    private GUIStyle sectionStyle;
    private bool showButtons = true;
    private bool showSettings = true;

    private void OnEnable()
    {
        controller = (BikeUIController)target;
    }

    public override void OnInspectorGUI()
    {
        InitializeStyles();
        
        // Personal Branding Header
        DrawBrandingHeader();
        
        EditorGUILayout.Space(10);
        
        // Main Content
        DrawMainContent();
        
        // Apply changes
        if (GUI.changed)
        {
            EditorUtility.SetDirty(controller);
        }
    }

    private void InitializeStyles()
    {
        if (headerStyle == null)
        {
            headerStyle = new GUIStyle(EditorStyles.boldLabel);
            headerStyle.fontSize = 16;
            headerStyle.normal.textColor = new Color(0.2f, 0.6f, 1f);
            headerStyle.alignment = TextAnchor.MiddleCenter;
        }

        if (brandingStyle == null)
        {
            brandingStyle = new GUIStyle(EditorStyles.label);
            brandingStyle.fontSize = 12;
            brandingStyle.fontStyle = FontStyle.Italic;
            brandingStyle.normal.textColor = new Color(0.7f, 0.7f, 0.7f);
            brandingStyle.alignment = TextAnchor.MiddleCenter;
        }

        if (sectionStyle == null)
        {
            sectionStyle = new GUIStyle(EditorStyles.boldLabel);
            sectionStyle.fontSize = 14;
            sectionStyle.normal.textColor = new Color(0.3f, 0.7f, 0.3f);
        }
    }

    private void DrawBrandingHeader()
    {
        EditorGUILayout.BeginVertical("box");
        
        // Main title with attractive styling
        GUILayout.Label("🏍️ BIKE UI CONTROLLER 🏍️", headerStyle);
        EditorGUILayout.Space(5);
        
        // Personal branding
        GUILayout.Label("Developed by Ali Taj", brandingStyle);
        EditorGUILayout.Space(3);
        
        // Description
        GUIStyle descStyle = new GUIStyle(EditorStyles.label);
        descStyle.fontSize = 10;
        descStyle.normal.textColor = new Color(0.6f, 0.6f, 0.6f);
        descStyle.alignment = TextAnchor.MiddleCenter;
        descStyle.wordWrap = true;
        
        GUILayout.Label("Universal Touch UI Controller for All Motorbikes\nNo Prefab Spawning Issues • Easy Button Assignment", descStyle);
        
        EditorGUILayout.EndVertical();
    }

    private void DrawMainContent()
    {
        serializedObject.Update();

        // Current Active Bike Section
        EditorGUILayout.BeginVertical("box");
        GUILayout.Label("🎯 Active Bike Control", sectionStyle);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("currentBike"), new GUIContent("Current Bike", "The bike that will receive touch input"));
        EditorGUILayout.EndVertical();

        EditorGUILayout.Space(5);

        // Auto Bike Assignment Section
        EditorGUILayout.BeginVertical("box");
        GUILayout.Label("🤖 Auto Bike Assignment", sectionStyle);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("autoAssignBike"), new GUIContent("Auto Assign Bike", "Automatically assign nearest bike to player"));

        if (controller.autoAssignBike)
        {
            EditorGUI.indentLevel++;
            EditorGUILayout.PropertyField(serializedObject.FindProperty("Player"), new GUIContent("Player Transform", "Reference to the player transform for distance calculation"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("motorbikeTag"), new GUIContent("Motorbike Tag", "Tag used to identify motorbikes"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("detectionDistance"), new GUIContent("Detection Distance", "Maximum distance to detect bikes"));
            EditorGUI.indentLevel--;

            // Status display
            EditorGUILayout.Space(5);
            if (Application.isPlaying)
            {
                string statusText = controller.GetCurrentBike() != null ?
                    $"✅ Current Bike: {controller.GetCurrentBike().name}" :
                    "❌ No bike assigned";
                EditorGUILayout.HelpBox(statusText, MessageType.Info);
            }
        }
        EditorGUILayout.EndVertical();

        EditorGUILayout.Space(5);

        // Auto Find Settings Section
        showSettings = EditorGUILayout.Foldout(showSettings, "⚙️ Auto Find Settings", true);
        if (showSettings)
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.PropertyField(serializedObject.FindProperty("autoFindButtons"), new GUIContent("Auto Find Buttons", "Automatically find UI buttons by name"));
            
            if (controller.autoFindButtons)
            {
                EditorGUI.indentLevel++;
                EditorGUILayout.PropertyField(serializedObject.FindProperty("accelButtonName"), new GUIContent("Accel Button Name"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("brakeButtonName"), new GUIContent("Brake Button Name"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("handBrakeButtonName"), new GUIContent("Hand Brake Button Name"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("rightButtonName"), new GUIContent("Right Button Name"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("leftButtonName"), new GUIContent("Left Button Name"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("nitroButtonName"), new GUIContent("Nitro Button Name"));
                EditorGUI.indentLevel--;
            }
            EditorGUILayout.EndVertical();
        }

        EditorGUILayout.Space(5);

        // Touch UI Buttons Section
        showButtons = EditorGUILayout.Foldout(showButtons, "🎮 Touch UI Buttons", true);
        if (showButtons)
        {
            EditorGUILayout.BeginVertical("box");
            
            // Movement Controls
            GUILayout.Label("🚀 Movement Controls", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(serializedObject.FindProperty("accelButton"), new GUIContent("Accelerate Button"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("brakeButton"), new GUIContent("Brake Button"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("handBrakeButton"), new GUIContent("Hand Brake Button"));
            
            EditorGUILayout.Space(5);
            
            // Steering Controls
            GUILayout.Label("🎯 Steering Controls", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(serializedObject.FindProperty("rightButton"), new GUIContent("Right Turn Button"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("leftButton"), new GUIContent("Left Turn Button"));
            
            EditorGUILayout.Space(5);
            
            // Special Controls
            GUILayout.Label("⚡ Special Controls", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(serializedObject.FindProperty("nitroButton"), new GUIContent("Nitro Boost Button"));
            
            EditorGUILayout.EndVertical();
        }

        EditorGUILayout.Space(10);

        // Action Buttons
        EditorGUILayout.BeginVertical("box");
        GUILayout.Label("🔧 Quick Actions", sectionStyle);
        
        EditorGUILayout.BeginHorizontal();
        
        if (GUILayout.Button("🔄 Reassign Buttons", GUILayout.Height(30)))
        {
            if (Application.isPlaying)
            {
                controller.ReassignButtons();
                Debug.Log("Buttons reassigned successfully!");
            }
            else
            {
                Debug.LogWarning("Reassign Buttons only works in Play Mode!");
            }
        }
        
        if (GUILayout.Button("🔍 Find Buttons", GUILayout.Height(30)))
        {
            // This will trigger the auto-find in the next frame
            controller.autoFindButtons = true;
            EditorUtility.SetDirty(controller);
        }
        
        EditorGUILayout.EndHorizontal();
        EditorGUILayout.EndVertical();

        // Help Box
        EditorGUILayout.Space(5);
        EditorGUILayout.HelpBox(
            "💡 Usage Tips:\n" +
            "• Place this script on a GameObject in your scene\n" +
            "• Assign buttons manually or use Auto Find\n" +
            "• Enable Auto Assign Bike for automatic proximity detection\n" +
            "• Set Player reference for auto assignment to work\n" +
            "• Works with all motorbike prefabs without issues",
            MessageType.Info
        );

        serializedObject.ApplyModifiedProperties();
    }
}
